//
//  ReportModels.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/8/20.
//

import SmartCodable

// MARK: - 举报字典响应模型

/// 举报字典响应
struct ReportDictResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var data: [ReportDictCategory] = []
    
    var isSuccess: Bool {
        return status == 1
    }
    
    var displayMessage: String {
        return errMsg.isEmpty ? "获取举报类型失败" : errMsg
    }
}

/// 举报字典分类
struct ReportDictCategory: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var dictDetails: [ReportDictDetail] = []
    var name: String = ""
    var description: String = ""
}

/// 举报字典详情
struct ReportDictDetail: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var label: String = ""
    var value: String = ""
    var dictSort: Int = 0
}

// MARK: - 举报提交请求模型

/// 举报提交请求
struct ReportSubmitRequest: SmartCodable {
    var description: String = ""
    var images: String = ""
    var labelValues: String = ""
    var reportCommentId: String = ""
    var reportCustomerId: String = ""
    var reportDescription: String = ""
    var reportImages: String = ""
    var reportUserInformationAvatar: String = ""
    var reportUserInformationImage: String = ""
    var reportUserInformationName: String = ""
    var reportUserInformationPersonalSignature: String = ""
    var reportUserInformationReason: String = ""
    var reportUserInformationType: String = ""
    var reportVideoIds: String = ""
    var status: Int = 0
    var type: Int = 0
}

// MARK: - 举报提交响应模型

/// 举报提交响应
struct ReportSubmitResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    
    var isSuccess: Bool {
        return status == 1
    }
    
    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return isSuccess ? "举报提交成功" : "举报提交失败"
    }
}

// MARK: - 举报页面数据模型

/// 举报页面使用的数据模型
struct ReportPageData {
    /// 当前举报的视频ID
    var videoId: String = ""
    /// 当前举报的用户ID
    var reportedUserId: String = ""
    /// 当前举报的用户名
    var reportedUserName: String = ""
    /// 当前举报的用户头像
    var reportedUserAvatar: String = ""
    /// 举报类型分类列表
    var categories: [ReportDictCategory] = []
    /// 选中的举报类型值
    var selectedReportValues: [String] = []
    /// 举报描述
    var reportDescription: String = ""
    /// 举报图片URL列表
    var reportImages: [String] = []
}
