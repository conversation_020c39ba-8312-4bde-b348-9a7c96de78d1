import UIKit
import SnapKit
import Kingfisher
import TXLiteAVSDK_UGC   // 你已经在 AppDelegate 里引入了

/// 图片轮播组件，专为 `VideoPage` 中的笔记作品设计。
/// 支持自动轮播 / 用户手动滑动接管 / 离开页面暂停。
final class NoteCarouselView: UIView {
    // MARK: - Public API
    /// 图片 URL 数组，赋值后自动刷新视图
    public var imageURLs: [String] = [] {
        didSet {
            pageControl.numberOfPages = imageURLs.count
            collectionView.reloadData()
            // 只有多张图片才需要自动轮播
            restartAutoScrollIfNeeded()
        }
    }

    /// 启动自动轮播（若图片 >1）
    public func startAutoScroll() { startTimer() }
    /// 停止自动轮播
    public func stopAutoScroll() { stopTimer() }

    // MARK: - Private Properties
    private let autoInterval: TimeInterval = 3
    private var timer: Timer?

    // MARK: UI
    private lazy var flowLayout: UICollectionViewFlowLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        return layout
    }()

    private lazy var collectionView: UICollectionView = {
        let cv = UICollectionView(frame: .zero, collectionViewLayout: flowLayout)
        cv.showsHorizontalScrollIndicator = false
        cv.isPagingEnabled = true
        cv.dataSource = self
        cv.delegate = self
        cv.backgroundColor = .black
        cv.register(NoteCarouselCell.self, forCellWithReuseIdentifier: NoteCarouselCell.reuseId)
        return cv
    }()

    private lazy var pageControl: UIPageControl = {
        let pc = UIPageControl()
        pc.currentPageIndicatorTintColor = .white
        pc.pageIndicatorTintColor = UIColor.white.withAlphaComponent(0.3)
        pc.isHidden = true // 隐藏圆点指示器，仅使用外部长条
        return pc
    }()

    /// 页面切换回调
    public var onPageChanged: ((Int) -> Void)?

    // MARK: - Indicator Bars
    private var indicatorStack: UIStackView?
    private var indicatorBars: [UIView] = []

    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        // 初始创建指示条（若有数据）
        setupIndicatorBars()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        // 初始创建指示条（若有数据）
        setupIndicatorBars()
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        flowLayout.itemSize = bounds.size
        // 在布局更新后确保指示条尺寸正确
        indicatorStack?.layoutIfNeeded()
    }

    deinit { stopTimer() }

    // MARK: - UI Setup
    private func setupUI() {
        addSubview(collectionView)
        collectionView.snp.makeConstraints { $0.edges.equalToSuperview() }

        addSubview(pageControl)
        pageControl.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(12)
        }
    }

    // MARK: - Auto Scroll
    private func startTimer() {
        guard timer == nil, imageURLs.count > 1 else { return }
        timer = Timer.scheduledTimer(timeInterval: autoInterval, target: self, selector: #selector(scrollToNext), userInfo: nil, repeats: true)
        RunLoop.main.add(timer!, forMode: .common)
    }

    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func restartAutoScrollIfNeeded() {
        stopTimer(); startTimer()
        // 更新指示条数量
        setupIndicatorBars()
    }

    @objc private func scrollToNext() {
        guard imageURLs.count > 1 else { return }
        let width = bounds.width
        guard width > 0, !width.isInfinite, !width.isNaN else { return }
        let visibleIndex = Int(collectionView.contentOffset.x / width)
        let nextIndex = (visibleIndex + 1) % imageURLs.count
        let indexPath = IndexPath(item: nextIndex, section: 0)
        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        pageControl.currentPage = nextIndex
        onPageChanged?(nextIndex)
        updateIndicator(selected: nextIndex)
    }
}

// MARK: - Indicator Helpers
private extension NoteCarouselView {
    func setupIndicatorBars() {
        // 清理旧的
        indicatorBars.removeAll()
        indicatorStack?.removeFromSuperview()
        guard imageURLs.count > 1 else { return }

        let stack = UIStackView()
        stack.axis = .horizontal
        stack.alignment = .fill
        stack.distribution = .fillEqually
        stack.spacing = 8
        addSubview(stack)
        stack.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().inset(0) // 与进度条同位置（父级控制）
            make.height.equalTo(4) // 与进度条高度一致
        }
        indicatorStack = stack

        for _ in 0..<imageURLs.count {
            let bar = UIView()
            bar.backgroundColor = UIColor.white.withAlphaComponent(0.6)
            bar.layer.cornerRadius = 2
            bar.clipsToBounds = true
            stack.addArrangedSubview(bar)
            indicatorBars.append(bar)
        }
        updateIndicator(selected: 0)
    }

    func updateIndicator(selected index: Int) {
        guard !indicatorBars.isEmpty else { return }
        for (i, bar) in indicatorBars.enumerated() {
            bar.backgroundColor = i == index ? .white : UIColor.white.withAlphaComponent(0.6)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension NoteCarouselView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        imageURLs.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: NoteCarouselCell.reuseId, for: indexPath) as! NoteCarouselCell
        let urlString = imageURLs[indexPath.item]
        if let url = URL(string: urlString) {
            let options: KingfisherOptionsInfo = [
                .transition(.fade(0.2)),
                .cacheOriginalImage
            ]
            cell.imageView.kf.setImage(with: url, options: options)
        } else {
            cell.imageView.image = nil
        }
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension NoteCarouselView: UICollectionViewDelegate {
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) { stopTimer() }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let width = bounds.width
        guard width > 0, !width.isInfinite, !width.isNaN else { return }
        let page = Int(scrollView.contentOffset.x / width)
        pageControl.currentPage = page
        onPageChanged?(page)
        updateIndicator(selected: page)
        // 2 秒后恢复自动轮播
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
            self?.startTimer()
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 预留点击回调，可扩展查看大图
    }
}

// MARK: - Cell
private final class NoteCarouselCell: UICollectionViewCell {
    static let reuseId = "NoteCarouselCell"

    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { $0.edges.equalToSuperview() }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
} 