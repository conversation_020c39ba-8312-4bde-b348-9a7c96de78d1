//
//  MentionUserCell.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/8/20.
//
// MARK: - 提及用户 Cell

import UIKit

class MentionUserCell: UICollectionViewCell {
    private let avatarImageView = UIImageView()
    private let nameLabel = UILabel()
    private let backgroundHighlightView = UIView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        // 背景高亮视图（用于点击反馈）
        backgroundHighlightView.backgroundColor = UIColor.black.withAlphaComponent(0.1)
        backgroundHighlightView.layer.cornerRadius = 8
        backgroundHighlightView.isHidden = true
        backgroundHighlightView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(backgroundHighlightView)

        // 头像设置
        avatarImageView.layer.cornerRadius = 24
        avatarImageView.clipsToBounds = true
        avatarImageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(avatarImageView)

        // 用户名标签设置
        nameLabel.font = UIFont.systemFont(ofSize: 13)
        nameLabel.textAlignment = .center
        nameLabel.numberOfLines = 1
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(nameLabel)

        // 约束设置
        NSLayoutConstraint.activate([
            // 背景高亮视图填满整个cell，增加点击范围
            backgroundHighlightView.topAnchor.constraint(equalTo: contentView.topAnchor),
            backgroundHighlightView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            backgroundHighlightView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            backgroundHighlightView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // 头像约束
            avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            avatarImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            avatarImageView.widthAnchor.constraint(equalToConstant: 48),
            avatarImageView.heightAnchor.constraint(equalToConstant: 48),

            // 用户名约束
            nameLabel.topAnchor.constraint(equalTo: avatarImageView.bottomAnchor, constant: 4),
            nameLabel.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 2),
            nameLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -2),
            nameLabel.heightAnchor.constraint(equalToConstant: 20)
        ])
    }

    func configure(with user: UserSearchResultsItem) {
        nameLabel.text = user.nickName
        if let url = URL(string: user.wxAvator), !user.wxAvator.isEmpty {
            avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar_placeholder"))
        } else {
            avatarImageView.image = UIImage(named: "avatar_placeholder")
        }
    }

    // 添加点击反馈效果
    override var isHighlighted: Bool {
        didSet {
            UIView.animate(withDuration: 0.1) {
                self.backgroundHighlightView.isHidden = !self.isHighlighted
                self.transform = self.isHighlighted ? CGAffineTransform(scaleX: 0.95, y: 0.95) : .identity
            }
        }
    }
}
