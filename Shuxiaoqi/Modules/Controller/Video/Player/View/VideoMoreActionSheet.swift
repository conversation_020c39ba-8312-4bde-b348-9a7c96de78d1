import UIKit

/// 视频更多操作类型
enum VideoMoreActionType: Int {
    case share = 0      // 分享
    case report = 1     // 举报
    case dislike = 2    // 不喜欢
}

/// 视频更多操作弹窗，UI样式与评论弹窗保持一致
class VideoMoreActionSheet: UIView {
    // MARK: - 属性
    private let actions: [VideoMoreActionType]
    private let actionHandler: (VideoMoreActionType) -> Void
    private let backgroundView = UIView()
    private let containerView = UIView()
    
    // MARK: - 初始化
    init(actions: [VideoMoreActionType] = [.share, .report, .dislike], actionHandler: @escaping (VideoMoreActionType) -> Void) {
        self.actions = actions
        self.actionHandler = actionHandler
        super.init(frame: UIScreen.main.bounds)
        setupUI()
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    // MARK: - UI搭建
    private func setupUI() {
        // 背景遮罩
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        backgroundView.frame = bounds
        addSubview(backgroundView)
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissSelf))
        backgroundView.addGestureRecognizer(tap)
        
        // 容器
        containerView.backgroundColor = UIColor(hex: "#EFEFEF")
        containerView.layer.cornerRadius = 16
        containerView.clipsToBounds = true
        addSubview(containerView)
        
        // 顶部装饰横条（20×2, 圆角）
        let handleView = UIView()
        handleView.backgroundColor = UIColor(hex: "#C4C4C4")
        handleView.layer.cornerRadius = 1
        containerView.addSubview(handleView)

        // 操作项 stack
        var lastView: UIView? = nil
        for (idx, action) in actions.enumerated() {
            let isFirst = idx == 0
            let isLast  = idx == actions.count - 1
            let cell = makeActionCell(for: action, isFirst: isFirst, isLast: isLast)
            containerView.addSubview(cell)
            cell.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                cell.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 8),
                cell.rightAnchor.constraint(equalTo: containerView.rightAnchor, constant: -8),
                cell.heightAnchor.constraint(equalToConstant: 40),
                cell.topAnchor.constraint(equalTo: lastView == nil ? containerView.topAnchor : lastView!.bottomAnchor, constant: lastView == nil ? 24 : 0)
            ])
            // 分割线（中间行）
            if !isLast {
                let sep = UIView()
                sep.backgroundColor = UIColor(hex: "#E7E7E7", alpha: 0.6)
                containerView.addSubview(sep)
                sep.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    sep.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 8),
                    sep.rightAnchor.constraint(equalTo: containerView.rightAnchor, constant: -8),
                    sep.heightAnchor.constraint(equalToConstant: 1),
                    sep.topAnchor.constraint(equalTo: cell.bottomAnchor)
                ])
                lastView = sep
            } else {
                lastView = cell
            }
        }
        // 顶部装饰横条布局
        handleView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            handleView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            handleView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            handleView.widthAnchor.constraint(equalToConstant: 20),
            handleView.heightAnchor.constraint(equalToConstant: 2)
        ])

        // 容器布局
        containerView.translatesAutoresizingMaskIntoConstraints = false
        let totalHeight = 24 + CGFloat(actions.count) * 41 - 1 + 12 + safeAreaInsets.bottom
        NSLayoutConstraint.activate([
            containerView.leftAnchor.constraint(equalTo: leftAnchor),
            containerView.rightAnchor.constraint(equalTo: rightAnchor),
            containerView.bottomAnchor.constraint(equalTo: safeAreaLayoutGuide.bottomAnchor),
            containerView.heightAnchor.constraint(equalToConstant: totalHeight)
        ])
    }
    
    // MARK: - 单个操作cell
    private func makeActionCell(for type: VideoMoreActionType, isFirst: Bool, isLast: Bool) -> UIView {
        let view = UIView()
        view.backgroundColor = .white
        let radius: CGFloat = 8
        if isFirst {
            view.layer.cornerRadius = radius
            view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        } else if isLast {
            view.layer.cornerRadius = radius
            view.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        } else {
            view.layer.cornerRadius = 0
        }
        view.clipsToBounds = true
        // 图标
        let icon = UIImageView()
        let iconName: String
        let title: String
        let titleColor: UIColor
        switch type {
        case .share:
            iconName = "video_more_action_share"
            title = "分享"
            titleColor = UIColor(hex: "#333333")
        case .report:
            iconName = "comment_action_report"
            title = "举报"
            titleColor = UIColor(hex: "#333333")
        case .dislike:
            iconName = "video_more_action_dislike"
            title = "不喜欢"
            titleColor = UIColor(hex: "#333333")
        }
        icon.image = UIImage(named: iconName)
        icon.contentMode = .scaleAspectFit
        view.addSubview(icon)
        icon.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            icon.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 12),
            icon.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            icon.widthAnchor.constraint(equalToConstant: 22),
            icon.heightAnchor.constraint(equalToConstant: 22)
        ])
        // 标题
        let label = UILabel()
        label.text = title
        label.textColor = titleColor
        label.font = .systemFont(ofSize: 16)
        view.addSubview(label)
        label.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            label.leftAnchor.constraint(equalTo: icon.rightAnchor, constant: 10),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
        // 点击手势
        let tap = UITapGestureRecognizer(target: self, action: #selector(actionTapped(_:)))
        view.addGestureRecognizer(tap)
        view.tag = type.rawValue
        // VoiceOver
        view.isAccessibilityElement = true
        view.accessibilityLabel = title
        return view
    }
    
    // MARK: - 展示/关闭
    func show(in parent: UIView) {
        parent.addSubview(self)
        self.alpha = 0
        // 初始位置在底部以下
        containerView.transform = CGAffineTransform(translationX: 0, y: containerView.bounds.height)
        UIView.animate(withDuration: 0.25, animations: {
            self.alpha = 1
            self.containerView.transform = .identity
        })
    }
    @objc private func dismissSelf() {
        UIView.animate(withDuration: 0.18, animations: {
            self.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: self.containerView.bounds.height)
        }) { _ in self.removeFromSuperview() }
    }
    
    // MARK: - 操作回调
    @objc private func actionTapped(_ sender: UITapGestureRecognizer) {
        guard let tag = sender.view?.tag, let type = VideoMoreActionType(rawValue: tag) else { return }
        dismissSelf()
        actionHandler(type)
    }
}
