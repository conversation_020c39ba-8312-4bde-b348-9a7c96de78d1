//
//  VideoReportViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/8/20.
//

import UIKit
import SnapKit

/// 视频举报页面
class VideoReportViewController: BaseViewController {
    
    // MARK: - 属性
    
    /// 举报页面数据
    private var reportData = ReportPageData()
    
    /// 举报字典分类数据
    private var reportCategories: [ReportDictCategory] = []
    
    /// 选中的举报类型按钮
    private var selectedReportButtons: [UIButton] = []
    
    // MARK: - UI组件
    
    /// 主滚动视图
    private let scrollView = UIScrollView()
    private let scrollContentView = UIView()
    
    /// 举报类型容器
    private let reportTypesContainer = UIView()
    
    /// 举报描述输入框
    private let descriptionTextView = UITextView()
    private let descriptionPlaceholderLabel = UILabel()
    private let descriptionCountLabel = UILabel()
    
    /// 图片上传容器
    private let imageUploadContainer = UIView()
    private let addImageButton = UIButton(type: .custom)
    private var uploadedImageViews: [UIImageView] = []
    
    /// 提交按钮
    private let submitButton = UIButton(type: .custom)
    
    // MARK: - 初始化
    
    /// 初始化举报页面
    /// - Parameters:
    ///   - videoId: 被举报的视频ID
    ///   - reportedUserId: 被举报的用户ID
    ///   - reportedUserName: 被举报的用户名
    ///   - reportedUserAvatar: 被举报的用户头像
    init(videoId: String, reportedUserId: String = "", reportedUserName: String = "", reportedUserAvatar: String = "") {
        super.init(nibName: nil, bundle: nil)
        
        reportData.videoId = videoId
        reportData.reportedUserId = reportedUserId
        reportData.reportedUserName = reportedUserName
        reportData.reportedUserAvatar = reportedUserAvatar
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        loadReportDict()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置导航栏
        navTitle = "举报理由"
        showBackButton = true
        
        // 设置背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        setupReportTypesContainer()
        setupDescriptionSection()
        setupImageUploadSection()
        setupSubmitButton()
        setupConstraints()
    }
    
    private func setupReportTypesContainer() {
        scrollContentView.addSubview(reportTypesContainer)
        reportTypesContainer.backgroundColor = .white
        
        // 添加提示文本
        let tipLabel = UILabel()
        tipLabel.text = "请选择符合的举报原因，帮助我们进行处理"
        tipLabel.font = UIFont.systemFont(ofSize: 14)
        tipLabel.textColor = UIColor(hex: "#666666")
        tipLabel.numberOfLines = 0
        reportTypesContainer.addSubview(tipLabel)
        
        tipLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    private func setupDescriptionSection() {
        // 描述输入区域
        let descriptionContainer = UIView()
        descriptionContainer.backgroundColor = .white
        scrollContentView.addSubview(descriptionContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报描述（必填）"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        descriptionContainer.addSubview(titleLabel)
        
        // 输入框
        descriptionTextView.backgroundColor = UIColor(hex: "#F8F8F8")
        descriptionTextView.layer.cornerRadius = 8
        descriptionTextView.font = UIFont.systemFont(ofSize: 14)
        descriptionTextView.textColor = UIColor(hex: "#333333")
        descriptionTextView.delegate = self
        descriptionContainer.addSubview(descriptionTextView)
        
        // 占位符
        descriptionPlaceholderLabel.text = "请详细描述号，以便我们更好的处理"
        descriptionPlaceholderLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionPlaceholderLabel.textColor = UIColor(hex: "#999999")
        descriptionTextView.addSubview(descriptionPlaceholderLabel)
        
        // 字数统计
        descriptionCountLabel.text = "0/100"
        descriptionCountLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionCountLabel.textColor = UIColor(hex: "#999999")
        descriptionCountLabel.textAlignment = .right
        descriptionContainer.addSubview(descriptionCountLabel)
        
        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        descriptionTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(100)
        }
        
        descriptionPlaceholderLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(8)
        }
        
        descriptionCountLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionTextView.snp.bottom).offset(8)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        self.descriptionContainer = descriptionContainer
    }
    
    private func setupImageUploadSection() {
        // 图片上传区域
        imageUploadContainer.backgroundColor = .white
        scrollContentView.addSubview(imageUploadContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "图片上传"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        imageUploadContainer.addSubview(titleLabel)
        
        // 提示文本
        let tipLabel = UILabel()
        tipLabel.text = "最多上传5张，最大5M"
        tipLabel.font = UIFont.systemFont(ofSize: 12)
        tipLabel.textColor = UIColor(hex: "#999999")
        imageUploadContainer.addSubview(tipLabel)
        
        // 添加图片按钮
        addImageButton.backgroundColor = UIColor(hex: "#F8F8F8")
        addImageButton.layer.cornerRadius = 8
        addImageButton.layer.borderWidth = 1
        addImageButton.layer.borderColor = UIColor(hex: "#E0E0E0").cgColor
        addImageButton.setImage(UIImage(systemName: "plus"), for: .normal)
        addImageButton.tintColor = UIColor(hex: "#999999")
        addImageButton.addTarget(self, action: #selector(addImageButtonTapped), for: .touchUpInside)
        imageUploadContainer.addSubview(addImageButton)
        
        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
        }
        
        tipLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-16)
        }
        
        addImageButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.width.height.equalTo(64)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupSubmitButton() {
        submitButton.backgroundColor = UIColor(hex: "#FF6B35")
        submitButton.setTitle("提交", for: .normal)
        submitButton.setTitleColor(.white, for: .normal)
        submitButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        submitButton.layer.cornerRadius = 8
        submitButton.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        scrollContentView.addSubview(submitButton)
    }
    
    private func setupConstraints() {
        reportTypesContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        descriptionContainer.snp.makeConstraints { make in
            make.top.equalTo(reportTypesContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        imageUploadContainer.snp.makeConstraints { make in
            make.top.equalTo(descriptionContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        submitButton.snp.makeConstraints { make in
            make.top.equalTo(imageUploadContainer.snp.bottom).offset(32)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-32)
        }
    }
    
    // MARK: - 私有属性
    private var descriptionContainer: UIView!

    // MARK: - 数据加载

    private func loadReportDict() {
        // 显示加载状态
        showLoadingIndicator()

        APIManager.shared.getReportDict { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.reportCategories = response.data
                        self?.setupReportTypeButtons()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func setupReportTypeButtons() {
        // 清除现有按钮
        reportTypesContainer.subviews.forEach { view in
            if view is UIButton {
                view.removeFromSuperview()
            }
        }

        var lastView: UIView? = nil

        // 为每个分类创建按钮组
        for category in reportCategories {
            // 分类标题
            let categoryLabel = UILabel()
            categoryLabel.text = category.description
            categoryLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            categoryLabel.textColor = UIColor(hex: "#333333")
            reportTypesContainer.addSubview(categoryLabel)

            categoryLabel.snp.makeConstraints { make in
                if let lastView = lastView {
                    make.top.equalTo(lastView.snp.bottom).offset(24)
                } else {
                    make.top.equalToSuperview().offset(60) // 在提示文本下方
                }
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = categoryLabel

            // 创建该分类下的选项按钮
            let buttonsContainer = createReportTypeButtons(for: category.dictDetails)
            reportTypesContainer.addSubview(buttonsContainer)

            buttonsContainer.snp.makeConstraints { make in
                make.top.equalTo(categoryLabel.snp.bottom).offset(12)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = buttonsContainer
        }

        // 更新容器高度约束
        if let lastView = lastView {
            lastView.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-16)
            }
        }
    }

    private func createReportTypeButtons(for details: [ReportDictDetail]) -> UIView {
        let container = UIView()

        var currentRow = 0
        var currentColumn = 0
        let maxColumns = 2
        let buttonWidth: CGFloat = (UIScreen.main.bounds.width - 64) / 2 // 减去左右边距和中间间距
        let buttonHeight: CGFloat = 40
        let horizontalSpacing: CGFloat = 16
        let verticalSpacing: CGFloat = 12

        for detail in details {
            let button = UIButton(type: .custom)
            button.setTitle(detail.label, for: .normal)
            button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            button.setTitleColor(.white, for: .selected)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.backgroundColor = UIColor(hex: "#F8F8F8")
            button.layer.cornerRadius = 8
            button.layer.borderWidth = 1
            button.layer.borderColor = UIColor(hex: "#E0E0E0").cgColor

            // 存储举报类型值
            button.accessibilityIdentifier = detail.value

            button.addTarget(self, action: #selector(reportTypeButtonTapped(_:)), for: .touchUpInside)
            container.addSubview(button)

            let x = CGFloat(currentColumn) * (buttonWidth + horizontalSpacing)
            let y = CGFloat(currentRow) * (buttonHeight + verticalSpacing)

            button.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(x)
                make.top.equalToSuperview().offset(y)
                make.width.equalTo(buttonWidth)
                make.height.equalTo(buttonHeight)
            }

            currentColumn += 1
            if currentColumn >= maxColumns {
                currentColumn = 0
                currentRow += 1
            }
        }

        // 设置容器高度
        let totalRows = (details.count + maxColumns - 1) / maxColumns
        let containerHeight = CGFloat(totalRows) * buttonHeight + CGFloat(max(0, totalRows - 1)) * verticalSpacing

        container.snp.makeConstraints { make in
            make.height.equalTo(containerHeight)
        }

        return container
    }

    // MARK: - 事件处理

    @objc private func reportTypeButtonTapped(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected

        if sender.isSelected {
            // 选中状态
            sender.backgroundColor = UIColor(hex: "#FF6B35")
            sender.layer.borderColor = UIColor(hex: "#FF6B35").cgColor
            selectedReportButtons.append(sender)

            // 添加到选中的举报类型值
            if let value = sender.accessibilityIdentifier {
                reportData.selectedReportValues.append(value)
            }
        } else {
            // 取消选中状态
            sender.backgroundColor = UIColor(hex: "#F8F8F8")
            sender.layer.borderColor = UIColor(hex: "#E0E0E0").cgColor
            selectedReportButtons.removeAll { $0 == sender }

            // 从选中的举报类型值中移除
            if let value = sender.accessibilityIdentifier {
                reportData.selectedReportValues.removeAll { $0 == value }
            }
        }

        updateSubmitButtonState()
    }

    @objc private func addImageButtonTapped() {
        // 检查图片数量限制
        if uploadedImageViews.count >= 5 {
            showToast("最多只能上传5张图片")
            return
        }

        // 显示图片选择器
        let imagePickerController = UIImagePickerController()
        imagePickerController.delegate = self
        imagePickerController.sourceType = .photoLibrary
        imagePickerController.allowsEditing = false
        present(imagePickerController, animated: true)
    }

    @objc private func submitButtonTapped() {
        // 验证输入
        guard !reportData.selectedReportValues.isEmpty else {
            showToast("请选择举报理由")
            return
        }

        guard !reportData.reportDescription.isEmpty else {
            showToast("请填写举报描述")
            return
        }

        // 构建举报请求数据
        var reportRequest = ReportSubmitRequest()
        reportRequest.reportVideoIds = reportData.videoId
        reportRequest.reportCustomerId = reportData.reportedUserId
        reportRequest.reportUserInformationName = reportData.reportedUserName
        reportRequest.reportUserInformationAvatar = reportData.reportedUserAvatar
        reportRequest.labelValues = reportData.selectedReportValues.joined(separator: ",")
        reportRequest.reportDescription = reportData.reportDescription
        reportRequest.reportImages = reportData.reportImages.joined(separator: ",")
        reportRequest.description = reportData.reportDescription
        reportRequest.images = reportData.reportImages.joined(separator: ",")
        reportRequest.type = 1 // 视频举报类型
        reportRequest.status = 0

        // 提交举报
        showLoadingIndicator()

        APIManager.shared.submitReport(reportData: reportRequest) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.showSuccessAlert()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func updateSubmitButtonState() {
        let hasSelectedReports = !reportData.selectedReportValues.isEmpty
        let hasDescription = !reportData.reportDescription.isEmpty

        submitButton.isEnabled = hasSelectedReports && hasDescription
        submitButton.alpha = submitButton.isEnabled ? 1.0 : 0.5
    }

    // MARK: - 辅助方法

    private func showLoadingIndicator() {
        // 这里可以显示加载指示器
        // 可以使用项目中现有的加载指示器组件
    }

    private func hideLoadingIndicator() {
        // 隐藏加载指示器
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessAlert() {
        let alert = UIAlertController(title: "举报成功", message: "感谢您的举报，我们会尽快处理", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }

    private func showToast(_ message: String) {
        // 这里可以使用项目中现有的Toast组件
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        present(alert, animated: true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - UITextViewDelegate

extension VideoReportViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // 更新占位符显示
        descriptionPlaceholderLabel.isHidden = !textView.text.isEmpty

        // 限制字数
        if textView.text.count > 100 {
            textView.text = String(textView.text.prefix(100))
        }

        // 更新字数统计
        descriptionCountLabel.text = "\(textView.text.count)/100"

        // 更新举报描述
        reportData.reportDescription = textView.text

        // 更新提交按钮状态
        updateSubmitButtonState()
    }
}

// MARK: - UIImagePickerControllerDelegate & UINavigationControllerDelegate

extension VideoReportViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        guard let image = info[.originalImage] as? UIImage else { return }

        // 添加图片到界面
        addImageToUploadContainer(image)

        // TODO: 这里应该上传图片到服务器并获取URL
        // 暂时使用本地路径作为占位
        reportData.reportImages.append("local_image_\(uploadedImageViews.count)")
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }

    private func addImageToUploadContainer(_ image: UIImage) {
        let imageView = UIImageView(image: image)
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor(hex: "#F8F8F8")

        // 添加删除按钮
        let deleteButton = UIButton(type: .custom)
        deleteButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        deleteButton.tintColor = UIColor(hex: "#FF4444")
        deleteButton.backgroundColor = .white
        deleteButton.layer.cornerRadius = 10
        deleteButton.tag = uploadedImageViews.count
        deleteButton.addTarget(self, action: #selector(deleteImageButtonTapped(_:)), for: .touchUpInside)

        imageView.addSubview(deleteButton)
        imageView.isUserInteractionEnabled = true

        imageUploadContainer.addSubview(imageView)
        uploadedImageViews.append(imageView)

        // 设置约束
        let index = uploadedImageViews.count - 1
        let column = index % 5 // 一行最多5张图片
        let x = 16 + CGFloat(column) * (64 + 8) // 16是左边距，8是图片间距

        imageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(x)
            make.top.equalTo(addImageButton.snp.top)
            make.width.height.equalTo(64)
        }

        deleteButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(-8)
            make.right.equalToSuperview().offset(8)
            make.width.height.equalTo(20)
        }

        // 更新添加按钮位置
        updateAddImageButtonPosition()
    }

    @objc private func deleteImageButtonTapped(_ sender: UIButton) {
        let index = sender.tag

        // 移除图片视图
        if index < uploadedImageViews.count {
            uploadedImageViews[index].removeFromSuperview()
            uploadedImageViews.remove(at: index)
            reportData.reportImages.remove(at: index)
        }

        // 重新布局剩余图片
        relayoutImageViews()

        // 更新添加按钮位置
        updateAddImageButtonPosition()
    }

    private func relayoutImageViews() {
        for (index, imageView) in uploadedImageViews.enumerated() {
            let column = index % 5
            let x = 16 + CGFloat(column) * (64 + 8)

            imageView.snp.remakeConstraints { make in
                make.left.equalToSuperview().offset(x)
                make.top.equalTo(addImageButton.snp.top)
                make.width.height.equalTo(64)
            }

            // 更新删除按钮的tag
            if let deleteButton = imageView.subviews.first(where: { $0 is UIButton }) as? UIButton {
                deleteButton.tag = index
            }
        }
    }

    private func updateAddImageButtonPosition() {
        let imageCount = uploadedImageViews.count

        if imageCount >= 5 {
            // 隐藏添加按钮
            addImageButton.isHidden = true
        } else {
            // 更新添加按钮位置
            addImageButton.isHidden = false
            let column = imageCount % 5
            let x = 16 + CGFloat(column) * (64 + 8)

            addImageButton.snp.remakeConstraints { make in
                make.left.equalToSuperview().offset(x)
                make.top.equalTo((imageUploadContainer.subviews.first { $0 is UILabel && ($0 as! UILabel).text == "图片上传" })!.snp.bottom).offset(12)
                make.width.height.equalTo(64)
                make.bottom.equalToSuperview().offset(-16)
            }
        }
    }
}
