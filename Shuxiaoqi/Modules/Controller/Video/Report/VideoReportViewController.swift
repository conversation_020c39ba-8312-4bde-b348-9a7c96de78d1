//
//  VideoReportViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/8/20.
//

import UIKit
import SnapKit
import HXPhotoPicker

/// 视频举报页面
class VideoReportViewController: BaseViewController {
    
    // MARK: - 属性
    
    /// 举报页面数据
    private var reportData = ReportPageData()
    
    /// 举报字典分类数据
    private var reportCategories: [ReportDictCategory] = []
    
    /// 选中的举报类型按钮
    private var selectedReportButtons: [UIButton] = []

    /// 选中的图片数组
    private var selectedImages: [UIImage] = [] {
        didSet {
            imageCollectionView.reloadData()
            updateSubmitButtonState()
        }
    }

    /// 最大图片数量
    private let maxImageCount = 5

    // MARK: - UI组件
    
    /// 主滚动视图
    private let scrollView = UIScrollView()
    private let scrollContentView = UIView()
    
    /// 举报类型容器
    private let reportTypesContainer = UIView()
    
    /// 举报描述输入框
    private let descriptionTextView = UITextView()
    private let descriptionPlaceholderLabel = UILabel()
    private let descriptionCountLabel = UILabel()
    
    /// 图片上传容器
    private let imageUploadContainer = UIView()

    /// 图片横向滚动视图
    private lazy var imageCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 90, height: 90) // 增加Cell尺寸为删除按钮预留空间
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.showsHorizontalScrollIndicator = false
        cv.isScrollEnabled = true
        cv.register(ReportImageCell.self, forCellWithReuseIdentifier: "ReportImageCell")
        cv.register(ReportAddImageCell.self, forCellWithReuseIdentifier: "ReportAddImageCell")
        cv.dataSource = self
        cv.delegate = self
        return cv
    }()
    
    /// 提交按钮
    private let submitButton = UIButton(type: .custom)
    
    // MARK: - 初始化
    
    /// 初始化举报页面
    /// - Parameters:
    ///   - videoId: 被举报的视频ID
    ///   - reportedUserId: 被举报的用户ID
    ///   - reportedUserName: 被举报的用户名
    ///   - reportedUserAvatar: 被举报的用户头像
    init(videoId: String, reportedUserId: String = "", reportedUserName: String = "", reportedUserAvatar: String = "") {
        super.init(nibName: nil, bundle: nil)
        
        reportData.videoId = videoId
        reportData.reportedUserId = reportedUserId
        reportData.reportedUserName = reportedUserName
        reportData.reportedUserAvatar = reportedUserAvatar
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        loadReportDict()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置导航栏
        navTitle = "举报理由"
        showBackButton = true

        // 设置背景色为白色
        contentView.backgroundColor = .white
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        setupReportTypesContainer()
        setupDescriptionSection()
        setupImageUploadSection()
        setupSubmitButton()
        setupConstraints()
    }
    
    private func setupReportTypesContainer() {
        scrollContentView.addSubview(reportTypesContainer)
        reportTypesContainer.backgroundColor = .white
        
        // 添加提示文本
        let tipLabel = UILabel()
        tipLabel.text = "请选择符合的举报原因，帮助我们进行处理"
        tipLabel.font = UIFont.systemFont(ofSize: 14)
        tipLabel.textColor = UIColor(hex: "#666666")
        tipLabel.numberOfLines = 0
        reportTypesContainer.addSubview(tipLabel)
        
        tipLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    private func setupDescriptionSection() {
        // 描述输入区域
        let descriptionContainer = UIView()
        descriptionContainer.backgroundColor = .white
        // 添加阴影效果
        descriptionContainer.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.15).cgColor
        descriptionContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        descriptionContainer.layer.shadowOpacity = 1
        descriptionContainer.layer.shadowRadius = 4
        descriptionContainer.layer.cornerRadius = 8
        scrollContentView.addSubview(descriptionContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报描述（必填）"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        descriptionContainer.addSubview(titleLabel)
        
        // 输入框 - 白色背景，无边框
        descriptionTextView.backgroundColor = .white
        descriptionTextView.layer.cornerRadius = 8
        descriptionTextView.font = UIFont.systemFont(ofSize: 14)
        descriptionTextView.textColor = UIColor(hex: "#333333")
        descriptionTextView.delegate = self
        descriptionContainer.addSubview(descriptionTextView)
        
        // 占位符
        descriptionPlaceholderLabel.text = "请详细描述号，以便我们更好的处理"
        descriptionPlaceholderLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionPlaceholderLabel.textColor = UIColor(hex: "#999999")
        descriptionTextView.addSubview(descriptionPlaceholderLabel)
        
        // 字数统计
        descriptionCountLabel.text = "0/100"
        descriptionCountLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionCountLabel.textColor = UIColor(hex: "#999999")
        descriptionCountLabel.textAlignment = .right
        descriptionContainer.addSubview(descriptionCountLabel)
        
        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        descriptionTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(100)
        }
        
        descriptionPlaceholderLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(8)
        }
        
        descriptionCountLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionTextView.snp.bottom).offset(8)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        self.descriptionContainer = descriptionContainer
    }
    
    private func setupImageUploadSection() {
        // 图片上传区域
        imageUploadContainer.backgroundColor = .white
        // 添加阴影效果
        imageUploadContainer.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.15).cgColor
        imageUploadContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        imageUploadContainer.layer.shadowOpacity = 1
        imageUploadContainer.layer.shadowRadius = 4
        imageUploadContainer.layer.cornerRadius = 8
        scrollContentView.addSubview(imageUploadContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "图片上传"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        imageUploadContainer.addSubview(titleLabel)
        
        // 提示文本
        let tipLabel = UILabel()
        tipLabel.text = "最多上传5张，最大5M"
        tipLabel.font = UIFont.systemFont(ofSize: 12)
        tipLabel.textColor = UIColor(hex: "#999999")
        imageUploadContainer.addSubview(tipLabel)

        // 图片横向滚动视图
        imageUploadContainer.addSubview(imageCollectionView)

        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
        }

        tipLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-16)
        }

        imageCollectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(90) // 增加高度为删除按钮预留空间
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupSubmitButton() {
        submitButton.backgroundColor = UIColor(hex: "#E5E5E5") // 初始状态为灰色
        submitButton.setTitle("提交", for: .normal)
        submitButton.setTitleColor(.white, for: .normal)
        submitButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        submitButton.layer.cornerRadius = 6
        submitButton.isEnabled = false // 初始状态不可交互
        submitButton.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        // 提交按钮固定在屏幕底部，不参与滚动
        contentView.addSubview(submitButton)
    }
    
    private func setupConstraints() {
        reportTypesContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview() // 移除容器的左右边距，让内部元素直接控制边距
        }
        
        descriptionContainer.snp.makeConstraints { make in
            make.top.equalTo(reportTypesContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        // imageUploadContainer约束已在setupConstraints中设置
        
        // 提交按钮固定在屏幕底部
        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(43)
            if #available(iOS 11.0, *) {
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            } else {
                make.bottom.equalToSuperview().offset(-16)
            }
        }

        // 更新滚动内容的底部约束，为提交按钮留出空间
        imageUploadContainer.snp.makeConstraints { make in
            make.top.equalTo(descriptionContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-80) // 为提交按钮留出空间
        }
    }
    
    // MARK: - 私有属性
    private var descriptionContainer: UIView!

    // MARK: - 数据加载

    private func loadReportDict() {
        // 显示加载状态
        showLoadingIndicator()

        APIManager.shared.getReportDict { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.reportCategories = response.data
                        self?.setupReportTypeButtons()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func setupReportTypeButtons() {
        // 清除现有按钮
        reportTypesContainer.subviews.forEach { view in
            if view is UIButton {
                view.removeFromSuperview()
            }
        }

        var lastView: UIView? = nil

        // 为每个分类创建按钮组
        for category in reportCategories {
            // 分类标题
            let categoryLabel = UILabel()
            categoryLabel.text = category.description
            categoryLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            categoryLabel.textColor = UIColor(hex: "#333333")
            reportTypesContainer.addSubview(categoryLabel)

            categoryLabel.snp.makeConstraints { make in
                if let lastView = lastView {
                    make.top.equalTo(lastView.snp.bottom).offset(24)
                } else {
                    make.top.equalToSuperview().offset(60) // 在提示文本下方
                }
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = categoryLabel

            // 创建该分类下的选项按钮
            let buttonsContainer = createReportTypeButtons(for: category.dictDetails)
            reportTypesContainer.addSubview(buttonsContainer)

            buttonsContainer.snp.makeConstraints { make in
                make.top.equalTo(categoryLabel.snp.bottom).offset(12)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = buttonsContainer
        }

        // 更新容器高度约束
        if let lastView = lastView {
            lastView.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-16)
            }
        }
    }

    private func createReportTypeButtons(for details: [ReportDictDetail]) -> UIView {
        let container = UIView()

        let buttonHeight: CGFloat = 30
        let horizontalSpacing: CGFloat = 12
        let verticalSpacing: CGFloat = 12
        let leftMargin: CGFloat = 16
        let rightMargin: CGFloat = 16
        let containerWidth = UIScreen.main.bounds.width - leftMargin - rightMargin

        var currentX: CGFloat = 0
        var currentY: CGFloat = 0

        for detail in details {
            let button = UIButton(type: .custom)
            button.setTitle(detail.label, for: .normal)
            button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            button.setTitleColor(UIColor(hex: "#FF8F1F"), for: .selected)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.backgroundColor = UIColor(hex: "#F5F5F5")
            button.layer.cornerRadius = 8
            button.layer.borderWidth = 0 // 默认无边框

            // 计算按钮宽度：文字宽度 + 24pt（左右各12pt）
            let textWidth = detail.label.size(withAttributes: [.font: UIFont.systemFont(ofSize: 14)]).width
            let buttonWidth = textWidth + 24

            // 检查当前行是否能放下这个按钮
            if currentX + buttonWidth > containerWidth && currentX > 0 {
                // 换行
                currentX = 0
                currentY += buttonHeight + verticalSpacing
            }

            // 存储举报类型值
            button.accessibilityIdentifier = detail.value

            button.addTarget(self, action: #selector(reportTypeButtonTapped(_:)), for: .touchUpInside)
            container.addSubview(button)

            button.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(currentX)
                make.top.equalToSuperview().offset(currentY)
                make.width.equalTo(buttonWidth)
                make.height.equalTo(buttonHeight)
            }

            currentX += buttonWidth + horizontalSpacing
        }

        // 设置容器高度
        let containerHeight = currentY + buttonHeight

        container.snp.makeConstraints { make in
            make.height.equalTo(containerHeight)
        }

        return container
    }

    // MARK: - 事件处理

    @objc private func reportTypeButtonTapped(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected

        if sender.isSelected {
            // 选中状态：白色背景，橙色边框和文字
            sender.backgroundColor = .white
            sender.layer.borderWidth = 1
            sender.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
            selectedReportButtons.append(sender)

            // 添加到选中的举报类型值
            if let value = sender.accessibilityIdentifier {
                reportData.selectedReportValues.append(value)
            }
        } else {
            // 取消选中状态：灰色背景，无边框
            sender.backgroundColor = UIColor(hex: "#F5F5F5")
            sender.layer.borderWidth = 0
            selectedReportButtons.removeAll { $0 == sender }

            // 从选中的举报类型值中移除
            if let value = sender.accessibilityIdentifier {
                reportData.selectedReportValues.removeAll { $0 == value }
            }
        }

        updateSubmitButtonState()
    }

    @objc private func addImageButtonTapped() {
        openImagePicker()
    }

    @objc private func submitButtonTapped() {
        // 验证输入
        guard !reportData.selectedReportValues.isEmpty else {
            showToast("请选择举报理由")
            return
        }

        guard !reportData.reportDescription.isEmpty else {
            showToast("请填写举报描述")
            return
        }

        // 如果有图片，先上传图片
        if !selectedImages.isEmpty {
            showLoadingIndicator()
            uploadImages(selectedImages) { [weak self] imageUrls in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    if imageUrls.isEmpty {
                        self.hideLoadingIndicator()
                        self.showErrorAlert(message: "图片上传失败，请重试")
                        return
                    }
                    // 图片上传成功，提交举报
                    self.submitReportWithImages(imageUrls)
                }
            }
        } else {
            // 没有图片，直接提交举报
            submitReportWithImages([])
        }
    }

    private func submitReportWithImages(_ imageUrls: [String]) {
        // 构建举报请求数据
        var reportRequest = ReportSubmitRequest()
        reportRequest.reportVideoIds = reportData.videoId
        reportRequest.reportCustomerId = reportData.reportedUserId
        reportRequest.reportUserInformationName = reportData.reportedUserName
        reportRequest.reportUserInformationAvatar = reportData.reportedUserAvatar
        reportRequest.labelValues = reportData.selectedReportValues.joined(separator: ",")
        reportRequest.reportDescription = reportData.reportDescription
        reportRequest.reportImages = imageUrls.joined(separator: ",")
        reportRequest.description = reportData.reportDescription
        reportRequest.images = imageUrls.joined(separator: ",")
        reportRequest.type = 1 // 视频举报类型
        reportRequest.status = 0

        // 提交举报
        if selectedImages.isEmpty {
            showLoadingIndicator()
        }

        APIManager.shared.submitReport(reportData: reportRequest) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.showSuccessAlert()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func updateSubmitButtonState() {
        let hasSelectedReports = !reportData.selectedReportValues.isEmpty
        let hasDescription = !reportData.reportDescription.isEmpty

        submitButton.isEnabled = hasSelectedReports && hasDescription

        if submitButton.isEnabled {
            submitButton.backgroundColor = UIColor(hex: "#FF8F1F")
        } else {
            submitButton.backgroundColor = UIColor(hex: "#E5E5E5")
        }
    }

    // MARK: - 图片上传

    /// 上传图片到七牛云，返回图片URL数组
    private func uploadImages(_ images: [UIImage], completion: @escaping ([String]) -> Void) {
        print("[举报] 开始上传图片，共\(images.count)张")
        let imageDatas = images.compactMap { $0.jpegData(compressionQuality: 0.9) }
        guard !imageDatas.isEmpty else {
            print("[举报] 图片数据为空")
            completion([])
            return
        }

        APIManager.shared.uploadFileQNRaw(files: imageDatas) { result in
            switch result {
            case .success(let response):
                print("[举报] 上传成功: \(response)")
                let urls = response.data?.flatMap { $0.data } ?? []
                completion(urls)
            case .failure(let error):
                print("[举报] 上传失败: \(error)")
                completion([])
            }
        }
    }

    // MARK: - 辅助方法

    private func showLoadingIndicator() {
        // 这里可以显示加载指示器
        // 可以使用项目中现有的加载指示器组件
    }

    private func hideLoadingIndicator() {
        // 隐藏加载指示器
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessAlert() {
        let alert = UIAlertController(title: "举报成功", message: "感谢您的举报，我们会尽快处理", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }

    private func showToast(_ message: String) {
        // 这里可以使用项目中现有的Toast组件
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        present(alert, animated: true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - UITextViewDelegate

extension VideoReportViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // 更新占位符显示
        descriptionPlaceholderLabel.isHidden = !textView.text.isEmpty

        // 限制字数
        if textView.text.count > 100 {
            textView.text = String(textView.text.prefix(100))
        }

        // 更新字数统计
        descriptionCountLabel.text = "\(textView.text.count)/100"

        // 更新举报描述
        reportData.reportDescription = textView.text

        // 更新提交按钮状态
        updateSubmitButtonState()
    }
}

// MARK: - 图片选择器

extension VideoReportViewController {
    private func openImagePicker() {
        var config = PickerConfiguration()
        config.selectMode = .multiple
        config.selectOptions = .photo
        config.maximumSelectedCount = maxImageCount - selectedImages.count
        config.modalPresentationStyle = .fullScreen
        config.editor.modalPresentationStyle = .fullScreen

        // 使用HXPhotoPicker进行图片选择
        self.hx.present(
            picker: config,
            finish: { [weak self] result, _ in
                guard let self = self else { return }
                // 将选择结果转换为UIImage数组并追加到已选图片中
                result.getImage { images in
                    self.selectedImages.append(contentsOf: images)
                }
            },
            cancel: nil
        )
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate

extension VideoReportViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return selectedImages.count < maxImageCount ? selectedImages.count + 1 : selectedImages.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ReportAddImageCell", for: indexPath) as! ReportAddImageCell
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ReportImageCell", for: indexPath) as! ReportImageCell
            cell.configure(with: selectedImages[indexPath.item])
            cell.onDelete = { [weak self] in
                guard let self = self else { return }
                self.selectedImages.remove(at: indexPath.item)
            }
            return cell
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            openImagePicker()
        }
    }
}

// MARK: - 自定义Cell

/// 举报图片显示Cell
private class ReportImageCell: UICollectionViewCell {
    private let imageView = UIImageView()
    private let deleteButton = UIButton(type: .custom)
    var onDelete: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 图片视图 - 保持80x80尺寸，居中显示
        contentView.addSubview(imageView)
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        imageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 80, height: 80)) // 图片保持80x80
        }

        // 删除按钮
        deleteButton.setImage(UIImage(named: "icon_video_edit_image_clear"), for: .normal)
        deleteButton.backgroundColor = .white
        deleteButton.layer.cornerRadius = 10
        deleteButton.clipsToBounds = true
        deleteButton.layer.shadowColor = UIColor.black.withAlphaComponent(0.15).cgColor
        deleteButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        deleteButton.layer.shadowOpacity = 1
        deleteButton.layer.shadowRadius = 2
        deleteButton.addTarget(self, action: #selector(deleteTapped), for: .touchUpInside)
        contentView.addSubview(deleteButton)

        deleteButton.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 20, height: 20))
            make.centerX.equalTo(imageView.snp.right).offset(-2)
            make.centerY.equalTo(imageView.snp.top).offset(3)
        }
    }

    func configure(with image: UIImage) {
        imageView.image = image
    }

    @objc private func deleteTapped() {
        onDelete?()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
    }
}

/// 举报添加图片Cell
private class ReportAddImageCell: UICollectionViewCell {
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 创建80x80的背景视图，居中显示
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(hex: "#EDEDED")
        backgroundView.layer.cornerRadius = 8
        contentView.addSubview(backgroundView)

        backgroundView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 80, height: 80)) // 背景保持80x80
        }

        // 添加图标
        let iconImageView = UIImageView(image: UIImage(named: "icon_add_image_32"))
        iconImageView.contentMode = .scaleAspectFit
        backgroundView.addSubview(iconImageView)

        iconImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
    }
}
