import UIKit

/// 用户协议和隐私政策弹窗组件
/// 用于应用启动时显示用户协议确认弹窗
final class UserAgreementPopupView: UIView, UITextViewDelegate {
    
    // MARK: - UI Elements
    private let backgroundView = UIView()
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let scrollView = UIScrollView()
    private var contentTextView: UITextView!
    private let buttonStackView = UIStackView()
    private let agreeButton = UIButton(type: .system)
    private let disagreeButton = UIButton(type: .system)
    
    // MARK: - Properties
    private var onAgree: (() -> Void)?
    private var onDisagree: (() -> Void)?
    
    // MARK: - Initializer
    private init(onAgree: @escaping () -> Void, onDisagree: @escaping () -> Void) {
        super.init(frame: .zero)
        self.onAgree = onAgree
        self.onDisagree = onDisagree
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public API
    /// 显示用户协议弹窗
    /// - Parameters:
    ///   - onAgree: 用户点击"同意并继续"的回调
    ///   - onDisagree: 用户点击"不同意"的回调
    static func show(onAgree: @escaping () -> Void, onDisagree: @escaping () -> Void) {
        guard let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
        
        let popup = UserAgreementPopupView(onAgree: onAgree, onDisagree: onDisagree)
        popup.frame = keyWindow.bounds
        popup.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        keyWindow.addSubview(popup)
        
        // 添加动画效果
        popup.alpha = 0
        popup.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [], animations: {
            popup.alpha = 1
            popup.containerView.transform = .identity
        }, completion: nil)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 背景遮罩
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        addSubview(backgroundView)
        
        // 容器视图
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 16
        containerView.clipsToBounds = true
        addSubview(containerView)
        
        // 标题
        titleLabel.text = "个人信息保护指引"
        titleLabel.font = .boldSystemFont(ofSize: 18)
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        containerView.addSubview(titleLabel)
        
        // 滚动视图
        scrollView.showsVerticalScrollIndicator = true
        scrollView.alwaysBounceVertical = true
        containerView.addSubview(scrollView)
        
        // 内容文本 - 使用 UITextView 支持富文本和点击
        let contentTextView = UITextView()
        contentTextView.font = .systemFont(ofSize: 14)
        contentTextView.textColor = UIColor(hex: "#666666")
        contentTextView.backgroundColor = .clear
        contentTextView.isEditable = false
        contentTextView.isScrollEnabled = false
        contentTextView.textContainer.lineFragmentPadding = 0
        contentTextView.textContainerInset = .zero
        contentTextView.delegate = self
        // 设置链接颜色
        contentTextView.linkTextAttributes = [
            .foregroundColor: UIColor(hex: "#FF8F1F"),
            .underlineStyle: NSUnderlineStyle().rawValue
        ]
        contentTextView.attributedText = getAttributedAgreementText()
        scrollView.addSubview(contentTextView)

        // 保存 contentTextView 的引用
        self.contentTextView = contentTextView
        
        // 按钮容器
        buttonStackView.axis = .horizontal
        buttonStackView.distribution = .fillEqually
        buttonStackView.spacing = 12
        containerView.addSubview(buttonStackView)
        
        // 不同意按钮
        disagreeButton.setTitle("不同意", for: .normal)
        disagreeButton.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        disagreeButton.titleLabel?.font = .systemFont(ofSize: 16)
        disagreeButton.backgroundColor = UIColor(hex: "#F5F5F5")
        disagreeButton.layer.cornerRadius = 22
        disagreeButton.addTarget(self, action: #selector(disagreeButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(disagreeButton)
        
        // 同意按钮
        agreeButton.setTitle("同意并继续", for: .normal)
        agreeButton.setTitleColor(.white, for: .normal)
        agreeButton.titleLabel?.font = .boldSystemFont(ofSize: 16)
        agreeButton.backgroundColor = UIColor(hex: "#FF8F1F")
        agreeButton.layer.cornerRadius = 22
        agreeButton.addTarget(self, action: #selector(agreeButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(agreeButton)
    }
    
    private func setupConstraints() {
        // 禁用自动布局转换
        [backgroundView, containerView, titleLabel, scrollView, contentTextView, buttonStackView].forEach {
            $0.translatesAutoresizingMaskIntoConstraints = false
        }
        
        NSLayoutConstraint.activate([
            // 背景视图
            backgroundView.topAnchor.constraint(equalTo: topAnchor),
            backgroundView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // 容器视图
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.leadingAnchor.constraint(greaterThanOrEqualTo: leadingAnchor, constant: 40),
            containerView.trailingAnchor.constraint(lessThanOrEqualTo: trailingAnchor, constant: -40),
            containerView.widthAnchor.constraint(equalToConstant: 320),
            containerView.heightAnchor.constraint(equalToConstant: 480),
            
            // 标题
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            scrollView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            scrollView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            scrollView.bottomAnchor.constraint(equalTo: buttonStackView.topAnchor, constant: -20),
            
            // 内容文本
            contentTextView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentTextView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentTextView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentTextView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentTextView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 按钮容器
            buttonStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            buttonStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            buttonStackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20),
            buttonStackView.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    // MARK: - Actions
    @objc private func agreeButtonTapped() {
        dismissWithAnimation {
            self.onAgree?()
        }
    }
    
    @objc private func disagreeButtonTapped() {
        dismissWithAnimation {
            self.onDisagree?()
        }
    }
    
    private func dismissWithAnimation(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.25, animations: {
            self.alpha = 0
            self.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }) { _ in
            self.removeFromSuperview()
            completion()
        }
    }
    
    // MARK: - Content
    private func getAttributedAgreementText() -> NSAttributedString {
        let text = """
欢迎使用树小柒！
请充分阅读《用户协议》和《隐私政策》，我们将按照法律法规的规定，参照行业最佳实践保护您的个人信息及隐私安全。点击"同意并继续"按钮，表示您已同意上述协议和以下约定，并接受树小柒的全部功能服务。
我们尊重您的选择权，在您使用树小柒时，我们会通过上述协议、系统权限弹窗以及其他说明帮助您了解我们为您提供的服务，及收集、处理个人信息的方式，包括但不限于如下事项：
1. 为了上传或拍摄图片、视频，实现树小柒的拍照导入、本地图片导入、保存编辑后的图片等功能，需要使用您的摄像头、相册、麦克风权限。
2. 我们可能会申请地理位置权限，用于为您推荐您可能感兴趣的内容。
上述相册、相机、麦克风和地理位置等敏感权限均不会默认或强制开启收集信息，只有经过您的明示授权，我们才会在实现功能或服务时调用。我们会严格遵循隐私政策收集、使用您的信息，不会因您同意了隐私政策而进行强制捆绑式信息收集。
"""

        let attributedString = NSMutableAttributedString(string: text)

        // 设置默认样式
        let baseFontSize: CGFloat = 14
        let defaultAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: baseFontSize),
            .foregroundColor: UIColor(hex: "#666666")
        ]
        attributedString.addAttributes(defaultAttributes, range: NSRange(location: 0, length: text.count))

        // 设置《用户协议》的样式和链接
        let userAgreementRange = (text as NSString).range(of: "《用户协议》")
        if userAgreementRange.location != NSNotFound {
            attributedString.addAttributes([
                .foregroundColor: UIColor(hex: "#FF8F1F"),
                .link: "user_agreement"
            ], range: userAgreementRange)
        }

        // 设置《隐私政策》的样式和链接
        let privacyPolicyRange = (text as NSString).range(of: "《隐私政策》")
        if privacyPolicyRange.location != NSNotFound {
            attributedString.addAttributes([
                .foregroundColor: UIColor(hex: "#FF8F1F"),
                .link: "privacy_policy"
            ], range: privacyPolicyRange)
        }

        return attributedString
    }

    // MARK: - UITextViewDelegate
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        let urlString = URL.absoluteString

        if urlString == "user_agreement" {
            openUserAgreement()
            return false
        } else if urlString == "privacy_policy" {
            openPrivacyPolicy()
            return false
        }

        return true
    }

    private func openUserAgreement() {
        print("点击了用户协议链接")
        // 找到当前的视图控制器
        if let viewController = findParentViewController() {
            print("找到了视图控制器: \(viewController)")
            SimpleWebViewController.showUserAgreement(from: viewController)
        } else {
            print("未找到视图控制器")
            // 备用方案：使用keyWindow的rootViewController
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
               let rootVC = keyWindow.rootViewController {
                print("使用keyWindow的rootViewController")
                SimpleWebViewController.showUserAgreement(from: rootVC)
            }
        }
    }

    private func openPrivacyPolicy() {
        print("点击了隐私政策链接")
        // 找到当前的视图控制器
        if let viewController = findParentViewController() {
            print("找到了视图控制器: \(viewController)")
            SimpleWebViewController.showPrivacyPolicy(from: viewController)
        } else {
            print("未找到视图控制器")
            // 备用方案：使用keyWindow的rootViewController
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
               let rootVC = keyWindow.rootViewController {
                print("使用keyWindow的rootViewController")
                SimpleWebViewController.showPrivacyPolicy(from: rootVC)
            }
        }
    }

    private func findParentViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while responder != nil {
            if let viewController = responder as? UIViewController {
                return viewController
            }
            responder = responder?.next
        }
        return nil
    }
}
